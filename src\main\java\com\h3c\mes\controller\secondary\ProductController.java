package com.h3c.mes.controller.secondary;

import com.h3c.mes.common.result.Result;
import com.h3c.mes.entity.secondary.Product;
import com.h3c.mes.service.secondary.ProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 产品Controller（从数据源）
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@RestController
@RequestMapping("/api/secondary/product")
@RequiredArgsConstructor
public class ProductController {

    private final ProductService productService;

    /**
     * 分页查询产品列表
     */
    @GetMapping("/page")
    public Result<Page<Product>> page(@RequestParam(defaultValue = "0") Integer page,
                                      @RequestParam(defaultValue = "10") Integer size,
                                      @RequestParam(required = false) String productCode,
                                      @RequestParam(required = false) String productName,
                                      @RequestParam(required = false) Integer status) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Product> result = productService.findAll(pageable);
            return Result.success(result);
        } catch (Exception e) {
            System.err.println("分页查询产品列表失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }

    /**
     * 查询所有产品
     */
    @GetMapping("/list")
    public Result<List<Product>> list() {
        try {
            List<Product> products = productService.findAll();
            return Result.success(products);
        } catch (Exception e) {
            System.err.println("查询产品列表失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }

    /**
     * 根据ID查询产品
     */
    @GetMapping("/{id}")
    public Result<Product> getById(@PathVariable Long id) {
        try {
            Optional<Product> product = productService.findById(id);
            return product.map(Result::success).orElse(Result.error("产品不存在"));
        } catch (Exception e) {
            System.err.println("查询产品失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }

    /**
     * 根据产品编码查询产品
     */
    @GetMapping("/code/{productCode}")
    public Result<Product> getByProductCode(@PathVariable String productCode) {
        try {
            Optional<Product> product = productService.getByProductCode(productCode);
            return product.map(Result::success).orElse(Result.error("产品不存在"));
        } catch (Exception e) {
            System.err.println("根据产品编码查询产品失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }

    /**
     * 创建产品
     */
    @PostMapping
    public Result<Product> create(@RequestBody Product product) {
        try {
            Product savedProduct = productService.save(product);
            return Result.success("创建成功", savedProduct);
        } catch (Exception e) {
            System.err.println("创建产品失败: " + e.getMessage());
            return Result.error("创建失败");
        }
    }

    /**
     * 更新产品
     */
    @PutMapping
    public Result<Product> update(@RequestBody Product product) {
        try {
            Product updatedProduct = productService.save(product);
            return Result.success("更新成功", updatedProduct);
        } catch (Exception e) {
            System.err.println("更新产品失败: " + e.getMessage());
            return Result.error("更新失败");
        }
    }

    /**
     * 删除产品
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        try {
            productService.deleteById(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            System.err.println("删除产品失败: " + e.getMessage());
            return Result.error("删除失败");
        }
    }

    /**
     * 根据类别ID查询产品列表
     */
    @GetMapping("/category/{categoryId}")
    public Result<List<Product>> listByCategoryId(@PathVariable Long categoryId) {
        try {
            List<Product> products = productService.listByCategoryId(categoryId);
            return Result.success(products);
        } catch (Exception e) {
            System.err.println("根据类别ID查询产品列表失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }
}
