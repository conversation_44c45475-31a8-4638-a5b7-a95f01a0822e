package com.h3c.mes;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;

/**
 * MES九星系统启动类
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class MesNinestarsApplication {

    public static void main(String[] args) {
        SpringApplication.run(MesNinestarsApplication.class, args);
        System.out.println("=================================");
        System.out.println("MES九星系统启动成功！");
        System.out.println("访问地址：http://localhost:8080/mes");
        System.out.println("=================================");
    }
}
