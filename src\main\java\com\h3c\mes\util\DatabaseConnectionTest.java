package com.h3c.mes.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 数据库连接测试工具类
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
public class DatabaseConnectionTest {

    /**
     * 测试SQL Server连接
     */
    public static void testSqlServerConnection() {
        String url = "*****************************************************************************************";
        String username = "sa";
        String password = "Qwer!234";
        
        System.out.println("=== SQL Server 连接测试 ===");
        System.out.println("URL: " + url);
        System.out.println("Username: " + username);
        
        try {
            // 加载驱动
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            System.out.println("✅ SQL Server 驱动加载成功");
            
            // 测试连接
            try (Connection conn = DriverManager.getConnection(url, username, password)) {
                System.out.println("✅ SQL Server 连接成功");
                
                // 获取服务器信息
                try (Statement stmt = conn.createStatement()) {
                    ResultSet rs = stmt.executeQuery("SELECT @@VERSION as Version, @@SERVERNAME as ServerName, SUSER_NAME() as CurrentUser");
                    if (rs.next()) {
                        System.out.println("📋 服务器信息:");
                        System.out.println("   版本: " + rs.getString("Version").split("\n")[0]);
                        System.out.println("   服务器名: " + rs.getString("ServerName"));
                        System.out.println("   当前用户: " + rs.getString("CurrentUser"));
                    }
                }
                
                // 检查数据库是否存在
                try (Statement stmt = conn.createStatement()) {
                    ResultSet rs = stmt.executeQuery("SELECT name FROM sys.databases WHERE name = 'mes_db'");
                    if (rs.next()) {
                        System.out.println("✅ 数据库 mes_db 存在");
                    } else {
                        System.out.println("⚠️ 数据库 mes_db 不存在，需要创建");
                        System.out.println("   请执行: CREATE DATABASE mes_db;");
                    }
                }
                
            }
        } catch (Exception e) {
            System.err.println("❌ SQL Server 连接失败: " + e.getMessage());
            e.printStackTrace();
            
            // 提供解决建议
            System.out.println("\n🔧 可能的解决方案:");
            System.out.println("1. 检查SQL Server服务是否启动");
            System.out.println("2. 检查端口1433是否开放");
            System.out.println("3. 检查sa账户是否启用");
            System.out.println("4. 检查密码是否正确");
            System.out.println("5. 检查SQL Server是否允许远程连接");
        }
    }

    /**
     * 测试MySQL连接
     */
    public static void testMySqlConnection() {
        String url = "************************************************************************************************************************************************************************************";
        String username = "root";
        String password = "123456";
        
        System.out.println("\n=== MySQL 连接测试 ===");
        System.out.println("URL: " + url);
        System.out.println("Username: " + username);
        
        try {
            // 加载驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("✅ MySQL 驱动加载成功");
            
            // 测试连接
            try (Connection conn = DriverManager.getConnection(url, username, password)) {
                System.out.println("✅ MySQL 连接成功");
                
                // 获取服务器信息
                try (Statement stmt = conn.createStatement()) {
                    ResultSet rs = stmt.executeQuery("SELECT VERSION() as Version, DATABASE() as CurrentDB, USER() as CurrentUser");
                    if (rs.next()) {
                        System.out.println("📋 服务器信息:");
                        System.out.println("   版本: " + rs.getString("Version"));
                        System.out.println("   当前数据库: " + rs.getString("CurrentDB"));
                        System.out.println("   当前用户: " + rs.getString("CurrentUser"));
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("❌ MySQL 连接失败: " + e.getMessage());
        }
    }

    /**
     * 主方法，用于独立测试
     */
    public static void main(String[] args) {
        testMySqlConnection();
        testSqlServerConnection();
    }
}
