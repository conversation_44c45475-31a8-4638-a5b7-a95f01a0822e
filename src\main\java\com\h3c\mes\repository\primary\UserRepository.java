package com.h3c.mes.repository.primary;

import com.h3c.mes.entity.primary.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户Repository接口（主数据源）
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    Optional<User> findByUsernameAndDeleted(String username, Integer deleted);

    /**
     * 根据部门ID查询用户列表
     * 
     * @param deptId 部门ID
     * @return 用户列表
     */
    List<User> findByDeptIdAndDeletedOrderByCreateTimeDesc(Long deptId, Integer deleted);

    /**
     * 根据状态查询用户列表
     * 
     * @param status 状态
     * @return 用户列表
     */
    List<User> findByStatusAndDeletedOrderByCreateTimeDesc(Integer status, Integer deleted);

    /**
     * 根据用户名模糊查询
     * 
     * @param username 用户名
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE u.username LIKE %:username% AND u.deleted = 0")
    List<User> findByUsernameLike(@Param("username") String username);

    /**
     * 根据真实姓名模糊查询
     * 
     * @param realName 真实姓名
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE u.realName LIKE %:realName% AND u.deleted = 0")
    List<User> findByRealNameLike(@Param("realName") String realName);
}
