package com.h3c.mes.service.primary.impl;

import com.h3c.mes.entity.primary.User;
import com.h3c.mes.repository.primary.UserRepository;
import com.h3c.mes.service.primary.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 用户Service实现类（主数据源）
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Service
@Transactional(transactionManager = "primaryTransactionManager")
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;

    @Override
    public Page<User> findAll(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    @Override
    public List<User> findAll() {
        return userRepository.findAll();
    }

    @Override
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    @Override
    public User save(User user) {
        return userRepository.save(user);
    }

    @Override
    public Optional<User> getByUsername(String username) {
        return userRepository.findByUsernameAndDeleted(username, 0);
    }

    @Override
    public List<User> listByDeptId(Long deptId) {
        return userRepository.findByDeptIdAndDeletedOrderByCreateTimeDesc(deptId, 0);
    }

    @Override
    public List<User> listByStatus(Integer status) {
        return userRepository.findByStatusAndDeletedOrderByCreateTimeDesc(status, 0);
    }

    @Override
    public void deleteById(Long id) {
        userRepository.deleteById(id);
    }

    @Override
    public List<User> findByUsernameLike(String username) {
        return userRepository.findByUsernameLike(username);
    }

    @Override
    public List<User> findByRealNameLike(String realName) {
        return userRepository.findByRealNameLike(realName);
    }
}
