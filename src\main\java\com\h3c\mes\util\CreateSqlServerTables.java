package com.h3c.mes.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

/**
 * 创建SQL Server表工具类
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
public class CreateSqlServerTables {

    public static void main(String[] args) {
        String url = "*************************************************************************************************************";
        String username = "sa";
        String password = "Qwer!234";
        
        System.out.println("=== 创建SQL Server表结构 ===");
        
        try {
            // 加载驱动
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            System.out.println("✅ SQL Server 驱动加载成功");
            
            // 连接到数据库
            try (Connection conn = DriverManager.getConnection(url, username, password)) {
                System.out.println("✅ 连接到数据库 mes_db 成功");
                
                try (Statement stmt = conn.createStatement()) {
                    // 创建产品表
                    String createTableSQL = """
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='mes_product' AND xtype='U')
                        BEGIN
                            CREATE TABLE mes_product (
                                id BIGINT IDENTITY(1,1) PRIMARY KEY,
                                product_code NVARCHAR(50) NOT NULL UNIQUE,
                                product_name NVARCHAR(100) NOT NULL,
                                specification NVARCHAR(200),
                                model NVARCHAR(50),
                                unit NVARCHAR(20),
                                price DECIMAL(10,2),
                                category_id BIGINT,
                                status INT DEFAULT 1,
                                remark NVARCHAR(500),
                                create_time DATETIME2 DEFAULT GETDATE(),
                                update_time DATETIME2 DEFAULT GETDATE(),
                                create_by NVARCHAR(50),
                                update_by NVARCHAR(50),
                                deleted INT DEFAULT 0,
                                version INT DEFAULT 1
                            );
                            PRINT 'Table mes_product created successfully.';
                        END
                        ELSE
                        BEGIN
                            PRINT 'Table mes_product already exists.';
                        END
                        """;
                    
                    System.out.println("🔧 正在创建表 mes_product...");
                    stmt.execute(createTableSQL);
                    System.out.println("✅ 表创建完成");
                    
                    // 插入测试数据
                    String insertDataSQL = """
                        IF NOT EXISTS (SELECT * FROM mes_product WHERE product_code = 'P001')
                        BEGIN
                            INSERT INTO mes_product (product_code, product_name, specification, model, unit, price, category_id, status, remark, create_by, update_by, deleted, version) VALUES
                            ('P001', N'产品A', N'规格A', N'型号A', N'个', 100.00, 1, 1, N'测试产品A', 'system', 'system', 0, 1),
                            ('P002', N'产品B', N'规格B', N'型号B', N'个', 200.00, 1, 1, N'测试产品B', 'system', 'system', 0, 1),
                            ('P003', N'产品C', N'规格C', N'型号C', N'个', 300.00, 2, 1, N'测试产品C', 'system', 'system', 0, 1);
                            PRINT 'Test data inserted successfully.';
                        END
                        ELSE
                        BEGIN
                            PRINT 'Test data already exists.';
                        END
                        """;
                    
                    System.out.println("🔧 正在插入测试数据...");
                    stmt.execute(insertDataSQL);
                    System.out.println("✅ 测试数据插入完成");
                    
                    // 验证表和数据
                    var rs = stmt.executeQuery("SELECT COUNT(*) as count FROM mes_product");
                    if (rs.next()) {
                        int count = rs.getInt("count");
                        System.out.println("📋 产品表中共有 " + count + " 条记录");
                    }
                }
                
            }
            
            System.out.println("\n🎉 SQL Server表结构创建完成！现在可以启动应用了。");
            
        } catch (Exception e) {
            System.err.println("❌ 创建表失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
