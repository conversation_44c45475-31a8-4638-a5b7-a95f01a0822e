# MES九星系统

## 项目简介

MES九星系统是一个基于Spring Boot 3.2.0 + Spring Data JPA + 双数据源的制造执行系统，使用JDK 17开发。系统支持MySQL和SQL Server双数据源，实现了完整的用户管理和产品管理功能。

## 技术栈

- **JDK**: 17
- **Spring Boot**: 3.2.0
- **Spring Data JPA**: 3.2.0
- **Hibernate**: 6.3.1.Final
- **数据库连接池**: HikariCP
- **主数据库**: MySQL 8.0+
- **从数据库**: SQL Server 2017+
- **构建工具**: Maven
- **其他**: Lombok、Jackson

## 项目特性

- ✅ 双数据源配置（MySQL + SQL Server）
- ✅ Spring Data JPA 集成
- ✅ HikariCP 高性能连接池
- ✅ 统一响应结果封装
- ✅ JPA审计功能（自动填充创建时间、更新时间等）
- ✅ 乐观锁版本控制
- ✅ 分页查询支持
- ✅ RESTful API 设计
- ✅ 事务管理（多数据源独立事务）

## 项目结构

```
mes-ninestars/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/h3c/mes/
│   │   │       ├── MesNinestarsApplication.java          # 启动类
│   │   │       ├── common/                               # 公共模块
│   │   │       │   └── result/Result.java               # 统一响应结果
│   │   │       ├── config/                              # 配置类
│   │   │       │   ├── DataSourceConfig.java            # 双数据源配置
│   │   │       │   ├── PrimaryDataSourceConfig.java     # 主数据源Repository配置
│   │   │       │   ├── SecondaryDataSourceConfig.java   # 从数据源Repository配置
│   │   │       │   ├── JpaAuditingConfig.java           # JPA审计配置
│   │   │       │   └── MyMetaObjectHandler.java         # 元数据处理器
│   │   │       ├── controller/                          # 控制器
│   │   │       │   ├── TestController.java              # 测试控制器
│   │   │       │   ├── primary/UserController.java      # 用户控制器（主数据源）
│   │   │       │   └── secondary/ProductController.java # 产品控制器（从数据源）
│   │   │       ├── entity/                              # 实体类
│   │   │       │   ├── BaseEntity.java                  # 基础实体类
│   │   │       │   ├── primary/User.java                # 用户实体（主数据源）
│   │   │       │   └── secondary/Product.java           # 产品实体（从数据源）
│   │   │       ├── repository/                          # JPA Repository接口
│   │   │       │   ├── primary/UserRepository.java      # 用户Repository（主数据源）
│   │   │       │   └── secondary/ProductRepository.java # 产品Repository（从数据源）
│   │   │       ├── service/                             # 服务层
│   │   │       │   ├── primary/                         # 主数据源服务
│   │   │       │   │   ├── UserService.java
│   │   │       │   │   └── impl/UserServiceImpl.java
│   │   │       │   └── secondary/                       # 从数据源服务
│   │   │       │       ├── ProductService.java
│   │   │       │       └── impl/ProductServiceImpl.java
│   │   │       └── util/                                # 工具类
│   │   │           ├── DatabaseConnectionTest.java      # 数据库连接测试
│   │   │           ├── CreateSqlServerDatabase.java     # SQL Server数据库创建
│   │   │           └── CreateSqlServerTables.java       # SQL Server表创建
│   │   └── resources/
│   │       ├── application.yml                          # 默认配置
│   │       ├── application-dev.yml                      # 开发环境配置
│   │       ├── data.sql                                 # H2数据库初始化脚本
│   │       ├── init-database.sql                        # MySQL数据库初始化脚本
│   │       ├── init-sqlserver.sql                       # SQL Server初始化脚本
│   │       └── create-sqlserver-database.sql            # SQL Server数据库创建脚本
│   └── test/
│       └── java/
│           └── com/h3c/mes/
│               └── MesNinestarsApplicationTests.java    # 集成测试类
├── pom.xml                                             # Maven配置
└── README.md                                           # 项目说明
```

## 快速开始

### 1. 环境要求

- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- SQL Server 2017+

### 2. 数据库准备

#### 主数据源（MySQL）
```sql
-- 创建MySQL数据库（会自动创建）
-- 数据库名：mes_ninestars
-- 表会由JPA自动创建
```

#### 从数据源（SQL Server）
```sql
-- 1. 创建SQL Server数据库
CREATE DATABASE mes_db COLLATE Chinese_PRC_CI_AS;

-- 2. 运行表创建工具
mvn compile exec:java -Dexec.mainClass=com.h3c.mes.util.CreateSqlServerTables
```

### 3. 配置修改

修改 `src/main/resources/application-dev.yml` 中的数据库连接信息：

```yaml
spring:
  datasource:
    # 主数据源 - MySQL
    primary:
      jdbc-url: ******************************************************************************************************************************************************************************************************************
      username: root
      password: 123456

    # 从数据源 - SQL Server
    secondary:
      jdbc-url: **********************************************************************************************************************************************
      username: sa
      password: Qwer!234
```

### 4. 启动应用

```bash
# 编译项目
mvn clean compile

# 打包项目
mvn package -DskipTests

# 启动应用（开发环境）
java -jar target/mes-ninestars-1.0.0.jar --spring.profiles.active=dev
```

### 5. 访问应用

- 应用地址：http://localhost:8080/mes
- 健康检查：http://localhost:8080/mes/api/test/health

## API 接口

### 测试接口

- `GET /mes/api/test/health` - 健康检查 ✅
- `GET /mes/api/test/hello` - 测试接口 ✅

### 用户管理（主数据源 - MySQL）

- `GET /mes/api/primary/user/list` - 查询所有用户 ✅
- `GET /mes/api/primary/user/page?page=0&size=10` - 分页查询用户 ✅
- `GET /mes/api/primary/user/{id}` - 根据ID查询用户 ✅
- `GET /mes/api/primary/user/username/{username}` - 根据用户名查询用户 ✅
- `POST /mes/api/primary/user` - 创建用户 ✅
- `PUT /mes/api/primary/user` - 更新用户 ✅
- `DELETE /mes/api/primary/user/{id}` - 删除用户 ✅

### 产品管理（从数据源 - SQL Server）

- `GET /mes/api/secondary/product/list` - 查询所有产品 ✅
- `GET /mes/api/secondary/product/page?page=0&size=10` - 分页查询产品 ✅
- `GET /mes/api/secondary/product/{id}` - 根据ID查询产品 ✅
- `GET /mes/api/secondary/product/code/{productCode}` - 根据产品编码查询产品 ✅
- `POST /mes/api/secondary/product` - 创建产品 ✅
- `PUT /mes/api/secondary/product` - 更新产品 ✅
- `DELETE /mes/api/secondary/product/{id}` - 删除产品 ✅

### 响应格式

所有API接口都返回统一的响应格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1754303466252,
  "success": true
}
```

## 开发说明

### 双数据源架构

```
MES九星系统双数据源架构
├── 主数据源（MySQL）
│   ├── 数据库：mes_ninestars
│   ├── 实体包：com.h3c.mes.entity.primary
│   ├── Repository：com.h3c.mes.repository.primary
│   ├── 服务层：com.h3c.mes.service.primary
│   └── 控制器：com.h3c.mes.controller.primary
│
├── 从数据源（SQL Server 2017）
│   ├── 数据库：mes_db
│   ├── 实体包：com.h3c.mes.entity.secondary
│   ├── Repository：com.h3c.mes.repository.secondary
│   ├── 服务层：com.h3c.mes.service.secondary
│   └── 控制器：com.h3c.mes.controller.secondary
│
└── 配置层
    ├── DataSourceConfig：双数据源配置
    ├── PrimaryDataSourceConfig：主数据源Repository配置
    ├── SecondaryDataSourceConfig：从数据源Repository配置
    └── JpaAuditingConfig：审计功能配置
```

### 数据源使用说明

1. **主数据源（MySQL）**：用于系统管理相关的表（用户、角色、权限等）
2. **从数据源（SQL Server）**：用于业务相关的表（产品、订单、生产等）

### 添加新的实体和接口

1. 在对应的数据源包下创建JPA实体类
2. 创建对应的Repository接口（继承JpaRepository）
3. 创建Service接口和实现类
4. 创建Controller类
5. 注意事务管理器的使用（@Transactional指定transactionManager）

### 测试功能

项目包含完整的集成测试类 `MesNinestarsApplicationTests`，包含：
- 用户管理的增删改查测试
- 产品管理的增删改查测试
- 分页查询测试
- 双数据源功能验证

运行测试：
```bash
mvn test -Dtest=MesNinestarsApplicationTests
```

### 工具类使用

项目提供了几个实用的工具类：

1. **数据库连接测试**：
   ```bash
   mvn compile exec:java -Dexec.mainClass=com.h3c.mes.util.DatabaseConnectionTest
   ```

2. **创建SQL Server数据库**：
   ```bash
   mvn compile exec:java -Dexec.mainClass=com.h3c.mes.util.CreateSqlServerDatabase
   ```

3. **创建SQL Server表结构**：
   ```bash
   mvn compile exec:java -Dexec.mainClass=com.h3c.mes.util.CreateSqlServerTables
   ```

### 注意事项

- 不同数据源的事务是独立的，跨数据源操作需要特别注意事务管理
- Repository接口需要放在对应的包下，确保被正确扫描
- 实体类需要使用JPA注解进行映射
- SQL Server需要手动创建数据库和表结构
- 确保SQL Server服务正在运行且端口1433开放
- MySQL数据库会自动创建（通过createDatabaseIfNotExist=true参数）

### 数据库表结构

#### sys_user 表（MySQL - 主数据源）
- 用户管理相关数据
- 包含用户基本信息、状态、部门等字段
- 支持逻辑删除和乐观锁

#### mes_product 表（SQL Server - 从数据源）
- 产品管理相关数据
- 包含产品编码、名称、规格、价格等字段
- 支持逻辑删除和乐观锁

## 版本信息

- **当前版本**: 1.0.0
- **发布日期**: 2024-08-04

### 更新日志

#### v1.0.0 (2024-08-04)
- ✅ 实现双数据源配置（MySQL + SQL Server 2017）
- ✅ 集成Spring Data JPA替代MyBatis Plus
- ✅ 实现用户管理功能（主数据源）
- ✅ 实现产品管理功能（从数据源）
- ✅ 添加完整的集成测试
- ✅ 支持JPA审计功能
- ✅ 支持分页查询
- ✅ 统一响应结果封装
- ✅ 提供数据库连接和表创建工具

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request



---

**🎉 MES九星系统 - 基于Spring Boot 3.2.0 + JPA的双数据源制造执行系统**
