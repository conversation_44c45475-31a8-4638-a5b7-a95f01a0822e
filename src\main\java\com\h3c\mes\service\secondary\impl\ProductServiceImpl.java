package com.h3c.mes.service.secondary.impl;

import com.h3c.mes.entity.secondary.Product;
import com.h3c.mes.repository.secondary.ProductRepository;
import com.h3c.mes.service.secondary.ProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 产品Service实现类（从数据源）
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Service
@Transactional(transactionManager = "secondaryTransactionManager")
@RequiredArgsConstructor
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;

    @Override
    public Page<Product> findAll(Pageable pageable) {
        return productRepository.findAll(pageable);
    }

    @Override
    public List<Product> findAll() {
        return productRepository.findAll();
    }

    @Override
    public Optional<Product> findById(Long id) {
        return productRepository.findById(id);
    }

    @Override
    public Product save(Product product) {
        return productRepository.save(product);
    }

    @Override
    public Optional<Product> getByProductCode(String productCode) {
        return productRepository.findByProductCodeAndDeleted(productCode, 0);
    }

    @Override
    public List<Product> listByCategoryId(Long categoryId) {
        return productRepository.findByCategoryIdAndDeletedOrderByCreateTimeDesc(categoryId, 0);
    }

    @Override
    public List<Product> listByStatus(Integer status) {
        return productRepository.findByStatusAndDeletedOrderByCreateTimeDesc(status, 0);
    }

    @Override
    public void deleteById(Long id) {
        productRepository.deleteById(id);
    }

    @Override
    public List<Product> findByProductCodeLike(String productCode) {
        return productRepository.findByProductCodeLike(productCode);
    }

    @Override
    public List<Product> findByProductNameLike(String productName) {
        return productRepository.findByProductNameLike(productName);
    }
}
