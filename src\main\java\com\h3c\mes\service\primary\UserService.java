package com.h3c.mes.service.primary;

import com.h3c.mes.entity.primary.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 用户Service接口（主数据源）
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface UserService {

    /**
     * 分页查询所有用户
     */
    Page<User> findAll(Pageable pageable);

    /**
     * 查询所有用户
     */
    List<User> findAll();

    /**
     * 根据ID查询用户
     */
    Optional<User> findById(Long id);

    /**
     * 保存用户
     */
    User save(User user);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    Optional<User> getByUsername(String username);

    /**
     * 根据部门ID查询用户列表
     *
     * @param deptId 部门ID
     * @return 用户列表
     */
    List<User> listByDeptId(Long deptId);

    /**
     * 根据状态查询用户列表
     *
     * @param status 状态
     * @return 用户列表
     */
    List<User> listByStatus(Integer status);

    /**
     * 删除用户
     *
     * @param id 用户ID
     */
    void deleteById(Long id);

    /**
     * 根据用户名模糊查询
     */
    List<User> findByUsernameLike(String username);

    /**
     * 根据真实姓名模糊查询
     */
    List<User> findByRealNameLike(String realName);
}
