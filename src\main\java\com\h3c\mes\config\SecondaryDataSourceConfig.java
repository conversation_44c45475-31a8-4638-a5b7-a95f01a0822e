package com.h3c.mes.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import jakarta.persistence.EntityManagerFactory;

/**
 * 从数据源Repository配置 - SQL Server
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Configuration
@EnableJpaRepositories(
    basePackages = "com.h3c.mes.repository.secondary",
    entityManagerFactoryRef = "secondaryEntityManagerFactory",
    transactionManagerRef = "secondaryTransactionManager"
)
public class SecondaryDataSourceConfig {
}
