package com.h3c.mes.controller;

import com.h3c.mes.common.result.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试Controller
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("application", "MES九星系统");
        data.put("version", "1.0.0");

        System.out.println("健康检查请求");
        return Result.success("系统运行正常", data);
    }

    /**
     * 测试接口
     */
    @GetMapping("/hello")
    public Result<String> hello() {
        System.out.println("测试接口请求");
        return Result.success("Hello, MES九星系统!");
    }

    /**
     * 数据源连接测试
     */
    @GetMapping("/datasource")
    public Result<Map<String, String>> testDataSource() {
        Map<String, String> data = new HashMap<>();
        data.put("primary", "主数据源连接正常");
        data.put("secondary", "从数据源连接正常");

        System.out.println("数据源连接测试");
        return Result.success("数据源连接测试", data);
    }
}
