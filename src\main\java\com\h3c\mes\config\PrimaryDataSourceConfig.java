package com.h3c.mes.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import jakarta.persistence.EntityManagerFactory;

/**
 * 主数据源Repository配置 - MySQL
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Configuration
@EnableJpaRepositories(
    basePackages = "com.h3c.mes.repository.primary",
    entityManagerFactoryRef = "primaryEntityManagerFactory",
    transactionManagerRef = "primaryTransactionManager"
)
public class PrimaryDataSourceConfig {
}
