package com.h3c.mes;

import com.h3c.mes.entity.primary.User;
import com.h3c.mes.entity.secondary.Product;
import com.h3c.mes.service.primary.UserService;
import com.h3c.mes.service.secondary.ProductService;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MES系统集成测试类
 * 测试用户和产品的增删改查功能
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@SpringBootTest
@ActiveProfiles("dev")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class MesNinestarsApplicationTests {

    @Autowired
    private UserService userService;

    @Autowired
    private ProductService productService;

    private static Long testUserId;
    private static Long testProductId;

    @Test
    @Order(1)
    void contextLoads() {
        // 测试Spring上下文是否能正常加载
        assertNotNull(userService, "UserService should not be null");
        assertNotNull(productService, "ProductService should not be null");
        System.out.println("✅ Spring上下文加载成功");
    }

    // ==================== 用户管理测试 ====================

    @Test
    @Order(2)
    void testCreateUser() {
        System.out.println("\n🧪 测试创建用户...");

        // 创建测试用户
        User user = new User();
        user.setUsername("testuser001");
        user.setPassword("$2a$10$7JB720yubVSOfvVMe6/YqO4wkhWGEn4bJJnNpSn7TPQOhiVjOiDry"); // 加密后的密码
        user.setRealName("测试用户");
        user.setEmail("<EMAIL>");
        user.setPhone("13800138888");
        user.setStatus(1);
        user.setDeptId(1L);
        user.setRemark("这是一个测试用户");

        // 保存用户
        User savedUser = userService.save(user);

        // 验证结果
        assertNotNull(savedUser, "保存的用户不应为空");
        assertNotNull(savedUser.getId(), "用户ID不应为空");
        assertEquals("testuser001", savedUser.getUsername(), "用户名应该匹配");
        assertEquals("测试用户", savedUser.getRealName(), "真实姓名应该匹配");
        assertNotNull(savedUser.getCreateTime(), "创建时间不应为空");
        assertNotNull(savedUser.getUpdateTime(), "更新时间不应为空");

        // 保存ID用于后续测试
        testUserId = savedUser.getId();

        System.out.println("✅ 用户创建成功，ID: " + testUserId);
        System.out.println("   用户名: " + savedUser.getUsername());
        System.out.println("   真实姓名: " + savedUser.getRealName());
        System.out.println("   创建时间: " + savedUser.getCreateTime());
    }

    @Test
    @Order(3)
    void testFindUserById() {
        System.out.println("\n🧪 测试根据ID查询用户...");

        // 根据ID查询用户
        Optional<User> userOptional = userService.findById(testUserId);

        // 验证结果
        assertTrue(userOptional.isPresent(), "应该能找到用户");
        User user = userOptional.get();
        assertEquals("testuser001", user.getUsername(), "用户名应该匹配");
        assertEquals("测试用户", user.getRealName(), "真实姓名应该匹配");

        System.out.println("✅ 用户查询成功");
        System.out.println("   ID: " + user.getId());
        System.out.println("   用户名: " + user.getUsername());
        System.out.println("   邮箱: " + user.getEmail());
    }

    @Test
    @Order(4)
    void testFindUserByUsername() {
        System.out.println("\n🧪 测试根据用户名查询用户...");

        // 根据用户名查询用户
        Optional<User> userOptional = userService.getByUsername("testuser001");

        // 验证结果
        assertTrue(userOptional.isPresent(), "应该能找到用户");
        User user = userOptional.get();
        assertEquals(testUserId, user.getId(), "用户ID应该匹配");
        assertEquals("测试用户", user.getRealName(), "真实姓名应该匹配");

        System.out.println("✅ 根据用户名查询成功");
        System.out.println("   用户名: " + user.getUsername());
        System.out.println("   手机号: " + user.getPhone());
    }

    @Test
    @Order(5)
    void testUpdateUser() {
        System.out.println("\n🧪 测试更新用户...");

        // 先查询用户
        Optional<User> userOptional = userService.findById(testUserId);
        assertTrue(userOptional.isPresent(), "用户应该存在");

        User user = userOptional.get();
        String originalUpdateTime = user.getUpdateTime().toString();

        // 更新用户信息
        user.setRealName("更新后的测试用户");
        user.setEmail("<EMAIL>");
        user.setPhone("13900139999");
        user.setRemark("用户信息已更新");

        // 等待一秒确保更新时间不同
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 保存更新
        User updatedUser = userService.save(user);

        // 验证结果
        assertEquals("更新后的测试用户", updatedUser.getRealName(), "真实姓名应该已更新");
        assertEquals("<EMAIL>", updatedUser.getEmail(), "邮箱应该已更新");
        assertEquals("13900139999", updatedUser.getPhone(), "手机号应该已更新");
        assertNotEquals(originalUpdateTime, updatedUser.getUpdateTime().toString(), "更新时间应该已改变");

        System.out.println("✅ 用户更新成功");
        System.out.println("   更新后姓名: " + updatedUser.getRealName());
        System.out.println("   更新后邮箱: " + updatedUser.getEmail());
        System.out.println("   更新时间: " + updatedUser.getUpdateTime());
    }

    @Test
    @Order(6)
    void testFindAllUsersWithPagination() {
        System.out.println("\n🧪 测试用户分页查询...");

        // 创建分页参数
        Pageable pageable = PageRequest.of(0, 10);

        // 分页查询
        Page<User> userPage = userService.findAll(pageable);

        // 验证结果
        assertNotNull(userPage, "分页结果不应为空");
        assertTrue(userPage.getTotalElements() >= 1, "至少应该有一个用户");
        assertFalse(userPage.getContent().isEmpty(), "用户列表不应为空");

        System.out.println("✅ 用户分页查询成功");
        System.out.println("   总用户数: " + userPage.getTotalElements());
        System.out.println("   当前页用户数: " + userPage.getContent().size());
        System.out.println("   总页数: " + userPage.getTotalPages());

        // 打印用户信息
        userPage.getContent().forEach(user -> {
            System.out.println("   - 用户: " + user.getUsername() + " (" + user.getRealName() + ")");
        });
    }

    // ==================== 产品管理测试 ====================

    @Test
    @Order(7)
    void testCreateProduct() {
        System.out.println("\n🧪 测试创建产品...");

        // 创建测试产品
        Product product = new Product();
        product.setProductCode("TEST001");
        product.setProductName("测试产品A");
        product.setSpecification("测试规格A");
        product.setModel("TEST-MODEL-A");
        product.setUnit("个");
        product.setPrice(new BigDecimal("199.99"));
        product.setCategoryId(1L);
        product.setStatus(1);
        product.setRemark("这是一个测试产品");

        // 保存产品
        Product savedProduct = productService.save(product);

        // 验证结果
        assertNotNull(savedProduct, "保存的产品不应为空");
        assertNotNull(savedProduct.getId(), "产品ID不应为空");
        assertEquals("TEST001", savedProduct.getProductCode(), "产品编码应该匹配");
        assertEquals("测试产品A", savedProduct.getProductName(), "产品名称应该匹配");
        assertEquals(new BigDecimal("199.99"), savedProduct.getPrice(), "产品价格应该匹配");
        assertNotNull(savedProduct.getCreateTime(), "创建时间不应为空");

        // 保存ID用于后续测试
        testProductId = savedProduct.getId();

        System.out.println("✅ 产品创建成功，ID: " + testProductId);
        System.out.println("   产品编码: " + savedProduct.getProductCode());
        System.out.println("   产品名称: " + savedProduct.getProductName());
        System.out.println("   产品价格: " + savedProduct.getPrice());
        System.out.println("   创建时间: " + savedProduct.getCreateTime());
    }

    @Test
    @Order(8)
    void testFindProductById() {
        System.out.println("\n🧪 测试根据ID查询产品...");

        // 根据ID查询产品
        Optional<Product> productOptional = productService.findById(testProductId);

        // 验证结果
        assertTrue(productOptional.isPresent(), "应该能找到产品");
        Product product = productOptional.get();
        assertEquals("TEST001", product.getProductCode(), "产品编码应该匹配");
        assertEquals("测试产品A", product.getProductName(), "产品名称应该匹配");

        System.out.println("✅ 产品查询成功");
        System.out.println("   ID: " + product.getId());
        System.out.println("   产品编码: " + product.getProductCode());
        System.out.println("   规格: " + product.getSpecification());
        System.out.println("   型号: " + product.getModel());
    }

    @Test
    @Order(9)
    void testFindProductByCode() {
        System.out.println("\n🧪 测试根据产品编码查询产品...");

        // 根据产品编码查询产品
        Optional<Product> productOptional = productService.getByProductCode("TEST001");

        // 验证结果
        assertTrue(productOptional.isPresent(), "应该能找到产品");
        Product product = productOptional.get();
        assertEquals(testProductId, product.getId(), "产品ID应该匹配");
        assertEquals("测试产品A", product.getProductName(), "产品名称应该匹配");

        System.out.println("✅ 根据产品编码查询成功");
        System.out.println("   产品编码: " + product.getProductCode());
        System.out.println("   产品名称: " + product.getProductName());
        System.out.println("   单位: " + product.getUnit());
    }

    @Test
    @Order(10)
    void testUpdateProduct() {
        System.out.println("\n🧪 测试更新产品...");

        // 先查询产品
        Optional<Product> productOptional = productService.findById(testProductId);
        assertTrue(productOptional.isPresent(), "产品应该存在");

        Product product = productOptional.get();
        String originalUpdateTime = product.getUpdateTime().toString();

        // 更新产品信息
        product.setProductName("更新后的测试产品A");
        product.setPrice(new BigDecimal("299.99"));
        product.setSpecification("更新后的规格");
        product.setRemark("产品信息已更新");

        // 等待一秒确保更新时间不同
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 保存更新
        Product updatedProduct = productService.save(product);

        // 验证结果
        assertEquals("更新后的测试产品A", updatedProduct.getProductName(), "产品名称应该已更新");
        assertEquals(new BigDecimal("299.99"), updatedProduct.getPrice(), "产品价格应该已更新");
        assertEquals("更新后的规格", updatedProduct.getSpecification(), "产品规格应该已更新");
        assertNotEquals(originalUpdateTime, updatedProduct.getUpdateTime().toString(), "更新时间应该已改变");

        System.out.println("✅ 产品更新成功");
        System.out.println("   更新后名称: " + updatedProduct.getProductName());
        System.out.println("   更新后价格: " + updatedProduct.getPrice());
        System.out.println("   更新时间: " + updatedProduct.getUpdateTime());
    }

    @Test
    @Order(11)
    void testFindAllProductsWithPagination() {
        System.out.println("\n🧪 测试产品分页查询...");

        // 创建分页参数
        Pageable pageable = PageRequest.of(0, 10);

        // 分页查询
        Page<Product> productPage = productService.findAll(pageable);

        // 验证结果
        assertNotNull(productPage, "分页结果不应为空");
        assertTrue(productPage.getTotalElements() >= 1, "至少应该有一个产品");
        assertFalse(productPage.getContent().isEmpty(), "产品列表不应为空");

        System.out.println("✅ 产品分页查询成功");
        System.out.println("   总产品数: " + productPage.getTotalElements());
        System.out.println("   当前页产品数: " + productPage.getContent().size());
        System.out.println("   总页数: " + productPage.getTotalPages());

        // 打印产品信息
        productPage.getContent().forEach(product -> {
            System.out.println("   - 产品: " + product.getProductCode() + " (" + product.getProductName() + ") - ¥" + product.getPrice());
        });
    }

    // ==================== 删除测试 ====================

    @Test
    @Order(12)
    void testDeleteProduct() {
        System.out.println("\n🧪 测试删除产品...");

        // 删除产品
        productService.deleteById(testProductId);

        // 验证删除结果
        Optional<Product> productOptional = productService.findById(testProductId);
        assertFalse(productOptional.isPresent(), "产品应该已被删除");

        System.out.println("✅ 产品删除成功，ID: " + testProductId);
    }

    @Test
    @Order(13)
    void testDeleteUser() {
        System.out.println("\n🧪 测试删除用户...");

        // 删除用户
        userService.deleteById(testUserId);

        // 验证删除结果
        Optional<User> userOptional = userService.findById(testUserId);
        assertFalse(userOptional.isPresent(), "用户应该已被删除");

        System.out.println("✅ 用户删除成功，ID: " + testUserId);
    }

    @Test
    @Order(14)
    void testFinalVerification() {
        System.out.println("\n🧪 最终验证测试...");

        // 验证所有测试数据都已清理
        List<User> allUsers = userService.findAll();
        List<Product> allProducts = productService.findAll();

        System.out.println("✅ 最终验证完成");
        System.out.println("   剩余用户数: " + allUsers.size());
        System.out.println("   剩余产品数: " + allProducts.size());
        System.out.println("\n🎉 所有增删改查测试完成！");
    }
}
