package com.h3c.mes.entity.secondary;

import com.h3c.mes.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产品实体类（从数据源）
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "mes_product")
public class Product extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 产品编码
     */
    @Column(name = "product_code", unique = true, nullable = false, length = 50)
    private String productCode;

    /**
     * 产品名称
     */
    @Column(name = "product_name", nullable = false, length = 100)
    private String productName;

    /**
     * 产品规格
     */
    @Column(name = "specification", length = 200)
    private String specification;

    /**
     * 产品型号
     */
    @Column(name = "model", length = 50)
    private String model;

    /**
     * 单位
     */
    @Column(name = "unit", length = 20)
    private String unit;

    /**
     * 单价
     */
    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;

    /**
     * 产品类别ID
     */
    @Column(name = "category_id")
    private Long categoryId;

    /**
     * 状态（0：停用，1：启用）
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
