package com.h3c.mes.service.secondary;

import com.h3c.mes.entity.secondary.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 产品Service接口（从数据源）
 *
 * <AUTHOR>
 * @since 2024-08-04
 */
public interface ProductService {

    /**
     * 分页查询所有产品
     */
    Page<Product> findAll(Pageable pageable);

    /**
     * 查询所有产品
     */
    List<Product> findAll();

    /**
     * 根据ID查询产品
     */
    Optional<Product> findById(Long id);

    /**
     * 保存产品
     */
    Product save(Product product);

    /**
     * 根据产品编码查询产品
     *
     * @param productCode 产品编码
     * @return 产品信息
     */
    Optional<Product> getByProductCode(String productCode);

    /**
     * 根据产品类别ID查询产品列表
     *
     * @param categoryId 产品类别ID
     * @return 产品列表
     */
    List<Product> listByCategoryId(Long categoryId);

    /**
     * 根据状态查询产品列表
     *
     * @param status 状态
     * @return 产品列表
     */
    List<Product> listByStatus(Integer status);

    /**
     * 删除产品
     *
     * @param id 产品ID
     */
    void deleteById(Long id);

    /**
     * 根据产品编码模糊查询
     */
    List<Product> findByProductCodeLike(String productCode);

    /**
     * 根据产品名称模糊查询
     */
    List<Product> findByProductNameLike(String productName);
}
