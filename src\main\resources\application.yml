server:
  port: 8080
  servlet:
    context-path: /mes

spring:
  application:
    name: mes-ninestars
  profiles:
    active: dev

  # 主数据源配置
  datasource:
    primary:
      driver-class-name: org.h2.Driver
      url: jdbc:h2:mem:testdb_primary
      username: sa
      password:
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM DUAL
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,wall,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
        web-stat-filter:
          enabled: true
          url-pattern: /*
          exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
        stat-view-servlet:
          enabled: true
          url-pattern: /druid/*
          reset-enable: false
          login-username: admin
          login-password: admin123
          allow: 127.0.0.1

    # 从数据源配置
    secondary:
      driver-class-name: org.h2.Driver
      url: jdbc:h2:mem:testdb_secondary
      username: sa
      password:
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        initial-size: 5
        min-idle: 5
        max-active: 20
        max-wait: 60000
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1 FROM DUAL
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        pool-prepared-statements: true
        max-pool-prepared-statement-per-connection-size: 20
        filters: stat,wall,slf4j
        connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

# MyBatis Plus 配置
mybatis-plus:
  # 配置扫描通用枚举
  type-enums-package: com.h3c.mes.common.enums
  # 配置 MyBatis 日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启驼峰命名
    map-underscore-to-camel-case: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 配置逻辑删除
  global-config:
    db-config:
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除全局值（1表示已删除）
      logic-delete-value: 1
      # 逻辑未删除全局值（0表示未删除）
      logic-not-delete-value: 0
      # 主键类型
      id-type: auto

# 日志配置
logging:
  level:
    com.h3c.mes: debug
    org.springframework: info
    com.alibaba.druid: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/mes-ninestars.log
    max-size: 10MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: always
