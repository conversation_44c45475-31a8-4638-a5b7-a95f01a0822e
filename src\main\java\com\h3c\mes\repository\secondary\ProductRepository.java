package com.h3c.mes.repository.secondary;

import com.h3c.mes.entity.secondary.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 产品Repository接口（从数据源）
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

    /**
     * 根据产品编码查询产品
     * 
     * @param productCode 产品编码
     * @return 产品信息
     */
    Optional<Product> findByProductCodeAndDeleted(String productCode, Integer deleted);

    /**
     * 根据产品类别ID查询产品列表
     * 
     * @param categoryId 产品类别ID
     * @return 产品列表
     */
    List<Product> findByCategoryIdAndDeletedOrderByCreateTimeDesc(Long categoryId, Integer deleted);

    /**
     * 根据状态查询产品列表
     * 
     * @param status 状态
     * @return 产品列表
     */
    List<Product> findByStatusAndDeletedOrderByCreateTimeDesc(Integer status, Integer deleted);

    /**
     * 根据产品编码模糊查询
     * 
     * @param productCode 产品编码
     * @return 产品列表
     */
    @Query("SELECT p FROM Product p WHERE p.productCode LIKE %:productCode% AND p.deleted = 0")
    List<Product> findByProductCodeLike(@Param("productCode") String productCode);

    /**
     * 根据产品名称模糊查询
     * 
     * @param productName 产品名称
     * @return 产品列表
     */
    @Query("SELECT p FROM Product p WHERE p.productName LIKE %:productName% AND p.deleted = 0")
    List<Product> findByProductNameLike(@Param("productName") String productName);
}
