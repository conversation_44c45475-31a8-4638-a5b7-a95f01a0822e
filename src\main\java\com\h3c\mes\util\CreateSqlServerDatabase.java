package com.h3c.mes.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

/**
 * 创建SQL Server数据库工具类
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
public class CreateSqlServerDatabase {

    public static void main(String[] args) {
        String masterUrl = "*************************************************************************************************************";
        String username = "sa";
        String password = "Qwer!234";
        
        System.out.println("=== 创建SQL Server数据库 ===");
        
        try {
            // 加载驱动
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            System.out.println("✅ SQL Server 驱动加载成功");
            
            // 连接到master数据库
            try (Connection conn = DriverManager.getConnection(masterUrl, username, password)) {
                System.out.println("✅ 连接到master数据库成功");
                
                try (Statement stmt = conn.createStatement()) {
                    // 检查数据库是否存在
                    var rs = stmt.executeQuery("SELECT name FROM sys.databases WHERE name = 'mes_db'");
                    if (rs.next()) {
                        System.out.println("✅ 数据库 mes_db 已存在");
                    } else {
                        // 创建数据库
                        System.out.println("🔧 正在创建数据库 mes_db...");
                        stmt.executeUpdate("CREATE DATABASE mes_db COLLATE Chinese_PRC_CI_AS");
                        System.out.println("✅ 数据库 mes_db 创建成功！");
                    }
                }
                
                // 验证数据库创建
                try (Statement stmt = conn.createStatement()) {
                    var rs = stmt.executeQuery("SELECT name, database_id, create_date FROM sys.databases WHERE name = 'mes_db'");
                    if (rs.next()) {
                        System.out.println("📋 数据库信息:");
                        System.out.println("   名称: " + rs.getString("name"));
                        System.out.println("   ID: " + rs.getInt("database_id"));
                        System.out.println("   创建时间: " + rs.getTimestamp("create_date"));
                    }
                }
                
            }
            
            System.out.println("\n🎉 数据库准备完成！现在可以启动应用了。");
            
        } catch (Exception e) {
            System.err.println("❌ 创建数据库失败: " + e.getMessage());
            e.printStackTrace();
            
            System.out.println("\n🔧 请手动在SQL Server Management Studio中执行:");
            System.out.println("CREATE DATABASE mes_db COLLATE Chinese_PRC_CI_AS;");
        }
    }
}
