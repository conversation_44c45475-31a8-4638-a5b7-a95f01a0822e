package com.h3c.mes.controller.primary;

import com.h3c.mes.common.result.Result;
import com.h3c.mes.entity.primary.User;
import com.h3c.mes.service.primary.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 用户Controller（主数据源）
 * 
 * <AUTHOR>
 * @since 2024-08-04
 */
@RestController
@RequestMapping("/api/primary/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 分页查询用户列表
     */
    @GetMapping("/page")
    public Result<Page<User>> page(@RequestParam(defaultValue = "0") Integer page,
                                   @RequestParam(defaultValue = "10") Integer size,
                                   @RequestParam(required = false) String username,
                                   @RequestParam(required = false) String realName,
                                   @RequestParam(required = false) Integer status) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<User> result = userService.findAll(pageable);
            return Result.success(result);
        } catch (Exception e) {
            System.err.println("分页查询用户列表失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }

    /**
     * 查询所有用户
     */
    @GetMapping("/list")
    public Result<List<User>> list() {
        try {
            List<User> users = userService.findAll();
            return Result.success(users);
        } catch (Exception e) {
            System.err.println("查询用户列表失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }

    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    public Result<User> getById(@PathVariable Long id) {
        try {
            Optional<User> user = userService.findById(id);
            return user.map(Result::success).orElse(Result.error("用户不存在"));
        } catch (Exception e) {
            System.err.println("查询用户失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }

    /**
     * 根据用户名查询用户
     */
    @GetMapping("/username/{username}")
    public Result<User> getByUsername(@PathVariable String username) {
        try {
            Optional<User> user = userService.getByUsername(username);
            return user.map(Result::success).orElse(Result.error("用户不存在"));
        } catch (Exception e) {
            System.err.println("根据用户名查询用户失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }

    /**
     * 创建用户
     */
    @PostMapping
    public Result<User> create(@RequestBody User user) {
        try {
            User savedUser = userService.save(user);
            return Result.success("创建成功", savedUser);
        } catch (Exception e) {
            System.err.println("创建用户失败: " + e.getMessage());
            return Result.error("创建失败");
        }
    }

    /**
     * 更新用户
     */
    @PutMapping
    public Result<User> update(@RequestBody User user) {
        try {
            User updatedUser = userService.save(user);
            return Result.success("更新成功", updatedUser);
        } catch (Exception e) {
            System.err.println("更新用户失败: " + e.getMessage());
            return Result.error("更新失败");
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        try {
            userService.deleteById(id);
            return Result.success("删除成功");
        } catch (Exception e) {
            System.err.println("删除用户失败: " + e.getMessage());
            return Result.error("删除失败");
        }
    }

    /**
     * 根据部门ID查询用户列表
     */
    @GetMapping("/dept/{deptId}")
    public Result<List<User>> listByDeptId(@PathVariable Long deptId) {
        try {
            List<User> users = userService.listByDeptId(deptId);
            return Result.success(users);
        } catch (Exception e) {
            System.err.println("根据部门ID查询用户列表失败: " + e.getMessage());
            return Result.error("查询失败");
        }
    }
}
